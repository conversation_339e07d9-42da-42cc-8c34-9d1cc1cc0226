import { Injectable, BadRequestException, Inject, forwardRef } from '@nestjs/common';
import { PinoLogger, InjectPinoLogger } from 'nestjs-pino';
import { PrismaService } from '../../prisma/prisma.service';
import { PaymentStatus } from '@prisma/client';

export interface PaymentTermsCalculation {
  effectivePaymentTerms: number;
  calculatedDueDate: Date;
  isOverdue: boolean;
  daysOverdue: number;
  source: 'PURCHASE_ORDER' | 'SUPPLIER_DEFAULT' | 'MANUAL';
}

export interface PaymentSchedule {
  purchaseOrderId: string;
  orderNumber: string;
  orderDate: Date;
  totalAmount: number;
  paymentTerms: number;
  dueDate: Date;
  isOverdue: boolean;
  daysOverdue: number;
  totalPaid: number;
  remainingAmount: number;
  paymentStatus: PaymentStatus;
}

export interface PaymentTermsSummary {
  supplierId: string;
  supplierName: string;
  defaultPaymentTerms: number | null;
  totalOutstanding: number;
  totalOverdue: number;
  overdueCount: number;
  averagePaymentDays: number;
  complianceRate: number; // Percentage of payments made within terms
  paymentSchedules: PaymentSchedule[];
}

@Injectable()
export class PaymentTermsService {
  constructor(
    private prisma: PrismaService,
    @InjectPinoLogger(PaymentTermsService.name)
    private readonly logger: PinoLogger,
  ) {}

  // Monitoring service will be injected later to avoid circular dependency
  private monitoringService: any;

  /**
   * Calculate payment terms for a given purchase order or supplier
   * Priority: Purchase Order terms > Supplier default terms > 0 (immediate)
   * Uses read transaction for data consistency when multiple entities are involved
   */
  async calculatePaymentTerms(
    supplierId: string,
    purchaseOrderId?: string,
    manualTerms?: number,
  ): Promise<PaymentTermsCalculation> {
    this.logger.trace({ supplierId, purchaseOrderId, manualTerms }, 'Calculating payment terms with transaction isolation');

    try {
      // Priority 1: Manual terms (if provided) - no database access needed
      if (manualTerms !== undefined && manualTerms !== null) {
        const result: PaymentTermsCalculation = {
          effectivePaymentTerms: manualTerms,
          calculatedDueDate: new Date(Date.now() + (manualTerms * 24 * 60 * 60 * 1000)),
          isOverdue: false, // Manual terms are for new payments, so not overdue
          daysOverdue: 0,
          source: 'MANUAL',
        };

        this.logger.debug({ supplierId, manualTerms, result }, 'Using manual payment terms');
        return result;
      }

      // For database operations, use read transaction for consistency
      const result = await this.prisma.$transaction(async (prisma) => {
        let effectivePaymentTerms = 0;
        let source: 'PURCHASE_ORDER' | 'SUPPLIER_DEFAULT' | 'MANUAL' = 'SUPPLIER_DEFAULT';
        let orderDate = new Date();

        // Priority 2: Purchase order terms
        if (purchaseOrderId) {
          const purchaseOrder = await prisma.purchaseOrder.findUnique({
            where: { id: purchaseOrderId },
            select: { paymentTerms: true, orderDate: true, supplierId: true },
          });

          if (!purchaseOrder) {
            throw new BadRequestException('Purchase order tidak ditemukan');
          }

          if (purchaseOrder.supplierId !== supplierId) {
            throw new BadRequestException('Purchase order tidak sesuai dengan supplier');
          }

          orderDate = purchaseOrder.orderDate;

          if (purchaseOrder.paymentTerms !== null && purchaseOrder.paymentTerms > 0) {
            effectivePaymentTerms = purchaseOrder.paymentTerms;
            source = 'PURCHASE_ORDER';
            this.logger.debug({
              supplierId,
              purchaseOrderId,
              paymentTerms: purchaseOrder.paymentTerms
            }, 'Using purchase order payment terms');
          } else {
            // Fall back to supplier default - read within same transaction
            const supplier = await prisma.supplier.findUnique({
              where: { id: supplierId },
              select: { paymentTerms: true },
            });

            if (supplier?.paymentTerms && supplier.paymentTerms > 0) {
              effectivePaymentTerms = supplier.paymentTerms;
              source = 'SUPPLIER_DEFAULT';
              this.logger.debug({
                supplierId,
                paymentTerms: supplier.paymentTerms
              }, 'Using supplier default payment terms (fallback from PO)');
            }
          }
        }
        // Priority 3: Supplier default terms only
        else {
          const supplier = await prisma.supplier.findUnique({
            where: { id: supplierId },
            select: { paymentTerms: true },
          });

          if (!supplier) {
            throw new BadRequestException('Supplier tidak ditemukan');
          }

          if (supplier.paymentTerms && supplier.paymentTerms > 0) {
            effectivePaymentTerms = supplier.paymentTerms;
            source = 'SUPPLIER_DEFAULT';
            this.logger.debug({
              supplierId,
              paymentTerms: supplier.paymentTerms
            }, 'Using supplier default payment terms');
          }
        }

        // Calculate due date
        const calculatedDueDate = new Date(orderDate);
        calculatedDueDate.setDate(calculatedDueDate.getDate() + effectivePaymentTerms);

        // Check if overdue
        const today = new Date();
        today.setHours(0, 0, 0, 0); // Start of today
        const isOverdue = calculatedDueDate < today;
        const daysOverdue = isOverdue
          ? Math.floor((today.getTime() - calculatedDueDate.getTime()) / (1000 * 60 * 60 * 24))
          : 0;

        return {
          effectivePaymentTerms,
          calculatedDueDate,
          isOverdue,
          daysOverdue,
          source,
        };
      }, {
        isolationLevel: 'ReadCommitted' // Ensure consistent reads
      });

      this.logger.debug({
        supplierId,
        purchaseOrderId,
        result
      }, 'Payment terms calculated with transaction isolation');

      return result;
    } catch (error) {
      this.logger.error({
        err: error,
        supplierId,
        purchaseOrderId,
        manualTerms
      }, 'Failed to calculate payment terms');
      throw error;
    }
  }

  /**
   * Get payment schedule for a specific purchase order
   */
  async getPaymentSchedule(purchaseOrderId: string): Promise<PaymentSchedule> {
    this.logger.trace({ purchaseOrderId }, 'Getting payment schedule');

    try {
      const purchaseOrder = await this.prisma.purchaseOrder.findUnique({
        where: { id: purchaseOrderId },
        include: {
          supplier: {
            select: { name: true, paymentTerms: true },
          },
          payments: {
            where: {
              status: {
                in: [PaymentStatus.PAID, PaymentStatus.PARTIAL],
              },
            },
            select: { amount: true },
          },
        },
      });

      if (!purchaseOrder) {
        throw new BadRequestException('Purchase order tidak ditemukan');
      }

      // Calculate payment terms
      const paymentTermsCalc = await this.calculatePaymentTerms(
        purchaseOrder.supplierId,
        purchaseOrderId,
      );

      // Calculate payment amounts
      const totalPaid = purchaseOrder.payments.reduce(
        (sum, payment) => sum + Number(payment.amount),
        0,
      );
      const totalAmount = Number(purchaseOrder.totalAmount);
      const remainingAmount = totalAmount - totalPaid;

      const schedule: PaymentSchedule = {
        purchaseOrderId,
        orderNumber: purchaseOrder.orderNumber,
        orderDate: purchaseOrder.orderDate,
        totalAmount,
        paymentTerms: paymentTermsCalc.effectivePaymentTerms,
        dueDate: paymentTermsCalc.calculatedDueDate,
        isOverdue: paymentTermsCalc.isOverdue,
        daysOverdue: paymentTermsCalc.daysOverdue,
        totalPaid,
        remainingAmount,
        paymentStatus: purchaseOrder.paymentStatus,
      };

      this.logger.debug({ purchaseOrderId, schedule }, 'Payment schedule calculated');

      return schedule;
    } catch (error) {
      this.logger.error({
        err: error,
        purchaseOrderId
      }, 'Failed to get payment schedule');
      throw error;
    }
  }

  /**
   * Get comprehensive payment terms summary for a supplier
   * Uses transaction for data consistency across multiple related queries
   */
  async getPaymentTermsSummary(supplierId: string): Promise<PaymentTermsSummary> {
    this.logger.trace({ supplierId }, 'Getting payment terms summary with transaction consistency');

    try {
      // Use transaction to ensure consistent data across all related queries
      const result = await this.prisma.$transaction(async (prisma) => {
        // Get supplier info
        const supplier = await prisma.supplier.findUnique({
          where: { id: supplierId },
          select: { name: true, paymentTerms: true },
        });

        if (!supplier) {
          throw new BadRequestException('Supplier tidak ditemukan');
        }

        // Get all outstanding purchase orders within the same transaction
        const purchaseOrders = await prisma.purchaseOrder.findMany({
          where: {
            supplierId,
            paymentStatus: {
              in: [PaymentStatus.PENDING, PaymentStatus.PARTIAL, PaymentStatus.OVERDUE],
            },
          },
          include: {
            payments: {
              where: {
                status: {
                  in: [PaymentStatus.PAID, PaymentStatus.PARTIAL],
                },
              },
              select: { amount: true },
            },
          },
          orderBy: { orderDate: 'desc' },
        });

      // Calculate payment schedules for each order
      const paymentSchedules: PaymentSchedule[] = [];
      let totalOutstanding = 0;
      let totalOverdue = 0;
      let overdueCount = 0;

      for (const order of purchaseOrders) {
        const schedule = await this.getPaymentSchedule(order.id);
        paymentSchedules.push(schedule);

        totalOutstanding += schedule.remainingAmount;
        if (schedule.isOverdue && schedule.remainingAmount > 0) {
          totalOverdue += schedule.remainingAmount;
          overdueCount++;
        }
      }

        // Calculate compliance metrics within the same transaction
        const allPayments = await prisma.supplierPayment.findMany({
          where: {
            supplierId,
            status: PaymentStatus.PAID,
            purchaseOrderId: { not: null },
          },
          include: {
            purchaseOrder: {
              select: { orderDate: true },
            },
          },
        });

      let totalPaymentDays = 0;
      let onTimePayments = 0;

      for (const payment of allPayments) {
        if (payment.purchaseOrder && payment.effectivePaymentTerms !== null) {
          const orderDate = payment.purchaseOrder.orderDate;
          const paymentDate = payment.paymentDate;
          const actualDays = Math.floor(
            (paymentDate.getTime() - orderDate.getTime()) / (1000 * 60 * 60 * 24),
          );
          
          totalPaymentDays += actualDays;
          
          if (actualDays <= payment.effectivePaymentTerms) {
            onTimePayments++;
          }
        }
      }

      const averagePaymentDays = allPayments.length > 0 
        ? Math.round(totalPaymentDays / allPayments.length)
        : 0;
      
      const complianceRate = allPayments.length > 0 
        ? Math.round((onTimePayments / allPayments.length) * 100)
        : 100;

        return {
          supplierId,
          supplierName: supplier.name,
          defaultPaymentTerms: supplier.paymentTerms,
          totalOutstanding,
          totalOverdue,
          overdueCount,
          averagePaymentDays,
          complianceRate,
          paymentSchedules,
        };
      }, {
        isolationLevel: 'ReadCommitted' // Ensure consistent reads across all queries
      });

      this.logger.debug({ supplierId, summary: result }, 'Payment terms summary calculated with transaction consistency');

      return result;
    } catch (error) {
      this.logger.error({
        err: error,
        supplierId
      }, 'Failed to get payment terms summary');
      throw error;
    }
  }

  /**
   * Get all overdue payments across all suppliers
   */
  async getOverduePayments(): Promise<PaymentSchedule[]> {
    this.logger.trace({}, 'Getting all overdue payments');

    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // Get all purchase orders with outstanding amounts
      const purchaseOrders = await this.prisma.purchaseOrder.findMany({
        where: {
          paymentStatus: {
            in: [PaymentStatus.PENDING, PaymentStatus.PARTIAL, PaymentStatus.OVERDUE],
          },
        },
        include: {
          supplier: {
            select: { name: true, paymentTerms: true },
          },
          payments: {
            where: {
              status: {
                in: [PaymentStatus.PAID, PaymentStatus.PARTIAL],
              },
            },
            select: { amount: true },
          },
        },
        orderBy: { orderDate: 'asc' },
      });

      const overdueSchedules: PaymentSchedule[] = [];

      for (const order of purchaseOrders) {
        const schedule = await this.getPaymentSchedule(order.id);

        // Only include if overdue and has remaining amount
        if (schedule.isOverdue && schedule.remainingAmount > 0) {
          overdueSchedules.push(schedule);
        }
      }

      this.logger.info({
        overdueCount: overdueSchedules.length,
        totalOverdueAmount: overdueSchedules.reduce((sum, s) => sum + s.remainingAmount, 0)
      }, 'Overdue payments retrieved');

      return overdueSchedules;
    } catch (error) {
      this.logger.error({
        err: error
      }, 'Failed to get overdue payments');
      throw error;
    }
  }

  /**
   * Update payment status based on payment terms and current date
   * This method can be called periodically to update overdue statuses
   * Uses batch transactions to ensure atomicity and data consistency
   */
  async updateOverduePaymentStatuses(): Promise<{ updated: number; errors: number }> {
    this.logger.trace({}, 'Updating overdue payment statuses with transaction safety');

    try {
      const overdueSchedules = await this.getOverduePayments();

      if (overdueSchedules.length === 0) {
        this.logger.info({}, 'No overdue payments found to update');
        return { updated: 0, errors: 0 };
      }

      // Filter schedules that actually need updating
      const schedulesToUpdate = overdueSchedules.filter(
        schedule => schedule.paymentStatus !== PaymentStatus.OVERDUE
      );

      if (schedulesToUpdate.length === 0) {
        this.logger.info({
          totalOverdue: overdueSchedules.length,
          alreadyUpdated: overdueSchedules.length
        }, 'All overdue payments already have correct status');
        return { updated: 0, errors: 0 };
      }

      this.logger.debug({
        totalOverdue: overdueSchedules.length,
        needingUpdate: schedulesToUpdate.length
      }, 'Starting batch overdue status update');

      // Process updates in batches to avoid transaction timeouts
      const BATCH_SIZE = 50;
      let totalUpdated = 0;
      let totalErrors = 0;

      for (let i = 0; i < schedulesToUpdate.length; i += BATCH_SIZE) {
        const batch = schedulesToUpdate.slice(i, i + BATCH_SIZE);

        try {
          const batchResult = await this.updateOverduePaymentsBatch(batch);
          totalUpdated += batchResult.updated;
          totalErrors += batchResult.errors;

          this.logger.debug({
            batchNumber: Math.floor(i / BATCH_SIZE) + 1,
            batchSize: batch.length,
            batchUpdated: batchResult.updated,
            batchErrors: batchResult.errors
          }, 'Completed overdue payment batch update');

        } catch (error) {
          totalErrors += batch.length;
          this.logger.error({
            err: error,
            batchNumber: Math.floor(i / BATCH_SIZE) + 1,
            batchSize: batch.length
          }, 'Failed to process overdue payment batch');
        }
      }

      this.logger.info({
        totalProcessed: schedulesToUpdate.length,
        updated: totalUpdated,
        errors: totalErrors,
        successRate: totalUpdated / (totalUpdated + totalErrors) * 100
      }, 'Overdue payment status update completed');

      return { updated: totalUpdated, errors: totalErrors };

    } catch (error) {
      this.logger.error({
        err: error
      }, 'Failed to update overdue payment statuses');
      throw error;
    }
  }

  /**
   * Update a batch of overdue payments within a single transaction
   * Ensures atomicity - either all updates succeed or all fail
   */
  private async updateOverduePaymentsBatch(
    schedules: any[]
  ): Promise<{ updated: number; errors: number }> {
    this.logger.trace({
      batchSize: schedules.length
    }, 'Processing overdue payment batch in transaction');

    try {
      const result = await this.prisma.$transaction(async (prisma) => {
        const updatePromises = schedules.map(async (schedule) => {
          try {
            await prisma.purchaseOrder.update({
              where: { id: schedule.purchaseOrderId },
              data: {
                paymentStatus: PaymentStatus.OVERDUE,
                updatedAt: new Date()
              },
            });

            this.logger.debug({
              purchaseOrderId: schedule.purchaseOrderId,
              orderNumber: schedule.orderNumber,
              daysOverdue: schedule.daysOverdue
            }, 'Updated purchase order status to OVERDUE in transaction');

            return { success: true, purchaseOrderId: schedule.purchaseOrderId };
          } catch (error) {
            this.logger.error({
              err: error,
              purchaseOrderId: schedule.purchaseOrderId,
              orderNumber: schedule.orderNumber
            }, 'Failed to update purchase order in transaction');

            // Re-throw to fail the entire transaction
            throw error;
          }
        });

        // Wait for all updates to complete within the transaction
        const results = await Promise.all(updatePromises);

        return {
          updated: results.filter(r => r.success).length,
          errors: 0 // If we reach here, all succeeded
        };
      });

      this.logger.debug({
        batchSize: schedules.length,
        updated: result.updated
      }, 'Successfully completed overdue payment batch transaction');

      return result;

    } catch (error) {
      this.logger.error({
        err: error,
        batchSize: schedules.length
      }, 'Transaction failed for overdue payment batch - all changes rolled back');

      // Return all as errors since transaction failed
      return {
        updated: 0,
        errors: schedules.length
      };
    }
  }
}
