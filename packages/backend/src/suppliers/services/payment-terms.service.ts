import { Injectable, BadRequestException } from '@nestjs/common';
import { PinoLogger, InjectPinoLogger } from 'nestjs-pino';
import { PrismaService } from '../../prisma/prisma.service';
import { PaymentStatus } from '@prisma/client';

export interface PaymentTermsCalculation {
  effectivePaymentTerms: number;
  calculatedDueDate: Date;
  isOverdue: boolean;
  daysOverdue: number;
  source: 'PURCHASE_ORDER' | 'SUPPLIER_DEFAULT' | 'MANUAL';
}

export interface PaymentSchedule {
  purchaseOrderId: string;
  orderNumber: string;
  orderDate: Date;
  totalAmount: number;
  paymentTerms: number;
  dueDate: Date;
  isOverdue: boolean;
  daysOverdue: number;
  totalPaid: number;
  remainingAmount: number;
  paymentStatus: PaymentStatus;
}

export interface PaymentTermsSummary {
  supplierId: string;
  supplierName: string;
  defaultPaymentTerms: number | null;
  totalOutstanding: number;
  totalOverdue: number;
  overdueCount: number;
  averagePaymentDays: number;
  complianceRate: number; // Percentage of payments made within terms
  paymentSchedules: PaymentSchedule[];
}

@Injectable()
export class PaymentTermsService {
  constructor(
    private prisma: PrismaService,
    @InjectPinoLogger(PaymentTermsService.name)
    private readonly logger: PinoLogger,
  ) {}

  /**
   * Calculate payment terms for a given purchase order or supplier
   * Priority: Purchase Order terms > Supplier default terms > 0 (immediate)
   */
  async calculatePaymentTerms(
    supplierId: string,
    purchaseOrderId?: string,
    manualTerms?: number,
  ): Promise<PaymentTermsCalculation> {
    this.logger.trace({ supplierId, purchaseOrderId, manualTerms }, 'Calculating payment terms');

    try {
      let effectivePaymentTerms = 0;
      let source: 'PURCHASE_ORDER' | 'SUPPLIER_DEFAULT' | 'MANUAL' = 'MANUAL';
      let orderDate = new Date();

      // Priority 1: Manual terms (if provided)
      if (manualTerms !== undefined && manualTerms !== null) {
        effectivePaymentTerms = manualTerms;
        source = 'MANUAL';
        this.logger.debug({ supplierId, manualTerms }, 'Using manual payment terms');
      }
      // Priority 2: Purchase order terms
      else if (purchaseOrderId) {
        const purchaseOrder = await this.prisma.purchaseOrder.findUnique({
          where: { id: purchaseOrderId },
          select: { paymentTerms: true, orderDate: true, supplierId: true },
        });

        if (!purchaseOrder) {
          throw new BadRequestException('Purchase order tidak ditemukan');
        }

        if (purchaseOrder.supplierId !== supplierId) {
          throw new BadRequestException('Purchase order tidak sesuai dengan supplier');
        }

        orderDate = purchaseOrder.orderDate;

        if (purchaseOrder.paymentTerms !== null && purchaseOrder.paymentTerms > 0) {
          effectivePaymentTerms = purchaseOrder.paymentTerms;
          source = 'PURCHASE_ORDER';
          this.logger.debug({ 
            supplierId, 
            purchaseOrderId, 
            paymentTerms: purchaseOrder.paymentTerms 
          }, 'Using purchase order payment terms');
        } else {
          // Fall back to supplier default
          const supplier = await this.prisma.supplier.findUnique({
            where: { id: supplierId },
            select: { paymentTerms: true },
          });

          if (supplier?.paymentTerms && supplier.paymentTerms > 0) {
            effectivePaymentTerms = supplier.paymentTerms;
            source = 'SUPPLIER_DEFAULT';
            this.logger.debug({ 
              supplierId, 
              paymentTerms: supplier.paymentTerms 
            }, 'Using supplier default payment terms');
          }
        }
      }
      // Priority 3: Supplier default terms
      else {
        const supplier = await this.prisma.supplier.findUnique({
          where: { id: supplierId },
          select: { paymentTerms: true },
        });

        if (!supplier) {
          throw new BadRequestException('Supplier tidak ditemukan');
        }

        if (supplier.paymentTerms && supplier.paymentTerms > 0) {
          effectivePaymentTerms = supplier.paymentTerms;
          source = 'SUPPLIER_DEFAULT';
          this.logger.debug({ 
            supplierId, 
            paymentTerms: supplier.paymentTerms 
          }, 'Using supplier default payment terms');
        }
      }

      // Calculate due date
      const calculatedDueDate = new Date(orderDate);
      calculatedDueDate.setDate(calculatedDueDate.getDate() + effectivePaymentTerms);

      // Check if overdue
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Start of today
      const isOverdue = calculatedDueDate < today;
      const daysOverdue = isOverdue 
        ? Math.floor((today.getTime() - calculatedDueDate.getTime()) / (1000 * 60 * 60 * 24))
        : 0;

      const result: PaymentTermsCalculation = {
        effectivePaymentTerms,
        calculatedDueDate,
        isOverdue,
        daysOverdue,
        source,
      };

      this.logger.debug({
        supplierId,
        purchaseOrderId,
        result
      }, 'Payment terms calculated');

      return result;
    } catch (error) {
      this.logger.error({
        err: error,
        supplierId,
        purchaseOrderId,
        manualTerms
      }, 'Failed to calculate payment terms');
      throw error;
    }
  }

  /**
   * Get payment schedule for a specific purchase order
   */
  async getPaymentSchedule(purchaseOrderId: string): Promise<PaymentSchedule> {
    this.logger.trace({ purchaseOrderId }, 'Getting payment schedule');

    try {
      const purchaseOrder = await this.prisma.purchaseOrder.findUnique({
        where: { id: purchaseOrderId },
        include: {
          supplier: {
            select: { name: true, paymentTerms: true },
          },
          payments: {
            where: {
              status: {
                in: [PaymentStatus.PAID, PaymentStatus.PARTIAL],
              },
            },
            select: { amount: true },
          },
        },
      });

      if (!purchaseOrder) {
        throw new BadRequestException('Purchase order tidak ditemukan');
      }

      // Calculate payment terms
      const paymentTermsCalc = await this.calculatePaymentTerms(
        purchaseOrder.supplierId,
        purchaseOrderId,
      );

      // Calculate payment amounts
      const totalPaid = purchaseOrder.payments.reduce(
        (sum, payment) => sum + Number(payment.amount),
        0,
      );
      const totalAmount = Number(purchaseOrder.totalAmount);
      const remainingAmount = totalAmount - totalPaid;

      const schedule: PaymentSchedule = {
        purchaseOrderId,
        orderNumber: purchaseOrder.orderNumber,
        orderDate: purchaseOrder.orderDate,
        totalAmount,
        paymentTerms: paymentTermsCalc.effectivePaymentTerms,
        dueDate: paymentTermsCalc.calculatedDueDate,
        isOverdue: paymentTermsCalc.isOverdue,
        daysOverdue: paymentTermsCalc.daysOverdue,
        totalPaid,
        remainingAmount,
        paymentStatus: purchaseOrder.paymentStatus,
      };

      this.logger.debug({ purchaseOrderId, schedule }, 'Payment schedule calculated');

      return schedule;
    } catch (error) {
      this.logger.error({
        err: error,
        purchaseOrderId
      }, 'Failed to get payment schedule');
      throw error;
    }
  }

  /**
   * Get comprehensive payment terms summary for a supplier
   */
  async getPaymentTermsSummary(supplierId: string): Promise<PaymentTermsSummary> {
    this.logger.trace({ supplierId }, 'Getting payment terms summary');

    try {
      // Get supplier info
      const supplier = await this.prisma.supplier.findUnique({
        where: { id: supplierId },
        select: { name: true, paymentTerms: true },
      });

      if (!supplier) {
        throw new BadRequestException('Supplier tidak ditemukan');
      }

      // Get all outstanding purchase orders
      const purchaseOrders = await this.prisma.purchaseOrder.findMany({
        where: {
          supplierId,
          paymentStatus: {
            in: [PaymentStatus.PENDING, PaymentStatus.PARTIAL, PaymentStatus.OVERDUE],
          },
        },
        include: {
          payments: {
            where: {
              status: {
                in: [PaymentStatus.PAID, PaymentStatus.PARTIAL],
              },
            },
            select: { amount: true },
          },
        },
        orderBy: { orderDate: 'desc' },
      });

      // Calculate payment schedules for each order
      const paymentSchedules: PaymentSchedule[] = [];
      let totalOutstanding = 0;
      let totalOverdue = 0;
      let overdueCount = 0;

      for (const order of purchaseOrders) {
        const schedule = await this.getPaymentSchedule(order.id);
        paymentSchedules.push(schedule);

        totalOutstanding += schedule.remainingAmount;
        if (schedule.isOverdue && schedule.remainingAmount > 0) {
          totalOverdue += schedule.remainingAmount;
          overdueCount++;
        }
      }

      // Calculate compliance metrics
      const allPayments = await this.prisma.supplierPayment.findMany({
        where: {
          supplierId,
          status: PaymentStatus.PAID,
          purchaseOrderId: { not: null },
        },
        include: {
          purchaseOrder: {
            select: { orderDate: true },
          },
        },
      });

      let totalPaymentDays = 0;
      let onTimePayments = 0;

      for (const payment of allPayments) {
        if (payment.purchaseOrder && payment.effectivePaymentTerms !== null) {
          const orderDate = payment.purchaseOrder.orderDate;
          const paymentDate = payment.paymentDate;
          const actualDays = Math.floor(
            (paymentDate.getTime() - orderDate.getTime()) / (1000 * 60 * 60 * 24),
          );
          
          totalPaymentDays += actualDays;
          
          if (actualDays <= payment.effectivePaymentTerms) {
            onTimePayments++;
          }
        }
      }

      const averagePaymentDays = allPayments.length > 0 
        ? Math.round(totalPaymentDays / allPayments.length)
        : 0;
      
      const complianceRate = allPayments.length > 0 
        ? Math.round((onTimePayments / allPayments.length) * 100)
        : 100;

      const summary: PaymentTermsSummary = {
        supplierId,
        supplierName: supplier.name,
        defaultPaymentTerms: supplier.paymentTerms,
        totalOutstanding,
        totalOverdue,
        overdueCount,
        averagePaymentDays,
        complianceRate,
        paymentSchedules,
      };

      this.logger.debug({ supplierId, summary }, 'Payment terms summary calculated');

      return summary;
    } catch (error) {
      this.logger.error({
        err: error,
        supplierId
      }, 'Failed to get payment terms summary');
      throw error;
    }
  }

  /**
   * Get all overdue payments across all suppliers
   */
  async getOverduePayments(): Promise<PaymentSchedule[]> {
    this.logger.trace({}, 'Getting all overdue payments');

    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // Get all purchase orders with outstanding amounts
      const purchaseOrders = await this.prisma.purchaseOrder.findMany({
        where: {
          paymentStatus: {
            in: [PaymentStatus.PENDING, PaymentStatus.PARTIAL, PaymentStatus.OVERDUE],
          },
        },
        include: {
          supplier: {
            select: { name: true, paymentTerms: true },
          },
          payments: {
            where: {
              status: {
                in: [PaymentStatus.PAID, PaymentStatus.PARTIAL],
              },
            },
            select: { amount: true },
          },
        },
        orderBy: { orderDate: 'asc' },
      });

      const overdueSchedules: PaymentSchedule[] = [];

      for (const order of purchaseOrders) {
        const schedule = await this.getPaymentSchedule(order.id);

        // Only include if overdue and has remaining amount
        if (schedule.isOverdue && schedule.remainingAmount > 0) {
          overdueSchedules.push(schedule);
        }
      }

      this.logger.info({
        overdueCount: overdueSchedules.length,
        totalOverdueAmount: overdueSchedules.reduce((sum, s) => sum + s.remainingAmount, 0)
      }, 'Overdue payments retrieved');

      return overdueSchedules;
    } catch (error) {
      this.logger.error({
        err: error
      }, 'Failed to get overdue payments');
      throw error;
    }
  }

  /**
   * Update payment status based on payment terms and current date
   * This method can be called periodically to update overdue statuses
   */
  async updateOverduePaymentStatuses(): Promise<{ updated: number; errors: number }> {
    this.logger.trace({}, 'Updating overdue payment statuses');

    let updated = 0;
    let errors = 0;

    try {
      const overdueSchedules = await this.getOverduePayments();

      for (const schedule of overdueSchedules) {
        try {
          // Update purchase order status to OVERDUE if it's not already
          if (schedule.paymentStatus !== PaymentStatus.OVERDUE) {
            await this.prisma.purchaseOrder.update({
              where: { id: schedule.purchaseOrderId },
              data: { paymentStatus: PaymentStatus.OVERDUE },
            });
            updated++;

            this.logger.debug({
              purchaseOrderId: schedule.purchaseOrderId,
              orderNumber: schedule.orderNumber,
              daysOverdue: schedule.daysOverdue
            }, 'Updated purchase order status to OVERDUE');
          }
        } catch (error) {
          errors++;
          this.logger.error({
            err: error,
            purchaseOrderId: schedule.purchaseOrderId
          }, 'Failed to update overdue status for purchase order');
        }
      }

      this.logger.info({
        totalProcessed: overdueSchedules.length,
        updated,
        errors
      }, 'Overdue payment status update completed');

      return { updated, errors };
    } catch (error) {
      this.logger.error({
        err: error
      }, 'Failed to update overdue payment statuses');
      throw error;
    }
  }
}
