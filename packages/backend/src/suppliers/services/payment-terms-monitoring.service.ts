import { Injectable } from '@nestjs/common';
import { PinoLogger, InjectPinoLogger } from 'nestjs-pino';
import { PrismaService } from '../../prisma/prisma.service';
import { PaymentTermsService } from './payment-terms.service';
import { PaymentStatus } from '@prisma/client';

export interface PaymentComplianceMetrics {
  totalPayments: number;
  onTimePayments: number;
  latePayments: number;
  overduePayments: number;
  complianceRate: number;
  averagePaymentDays: number;
  totalOutstandingAmount: number;
  criticalOverdueCount: number;
  moderateOverdueCount: number;
  recentOverdueCount: number;
}

export interface SupplierComplianceReport {
  supplierId: string;
  supplierName: string;
  metrics: PaymentComplianceMetrics;
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  recommendations: string[];
}

@Injectable()
export class PaymentTermsMonitoringService {
  constructor(
    private prisma: PrismaService,
    private paymentTermsService: PaymentTermsService,
    @InjectPinoLogger(PaymentTermsMonitoringService.name)
    private readonly logger: PinoLogger,
  ) {}

  /**
   * Generate comprehensive payment compliance metrics for all suppliers
   */
  async generateComplianceReport(): Promise<{
    overallMetrics: PaymentComplianceMetrics;
    supplierReports: SupplierComplianceReport[];
    generatedAt: Date;
  }> {
    this.logger.info({}, 'Generating comprehensive payment compliance report');

    try {
      // Get all suppliers with payment activity
      const suppliers = await this.prisma.supplier.findMany({
        where: {
          payments: {
            some: {}
          }
        },
        select: {
          id: true,
          name: true,
          paymentTerms: true,
        }
      });

      const supplierReports: SupplierComplianceReport[] = [];
      let overallMetrics: PaymentComplianceMetrics = {
        totalPayments: 0,
        onTimePayments: 0,
        latePayments: 0,
        overduePayments: 0,
        complianceRate: 0,
        averagePaymentDays: 0,
        totalOutstandingAmount: 0,
        criticalOverdueCount: 0,
        moderateOverdueCount: 0,
        recentOverdueCount: 0,
      };

      // Generate report for each supplier
      for (const supplier of suppliers) {
        try {
          const supplierMetrics = await this.calculateSupplierMetrics(supplier.id);
          const riskLevel = this.assessRiskLevel(supplierMetrics);
          const recommendations = this.generateRecommendations(supplierMetrics, riskLevel);

          const supplierReport: SupplierComplianceReport = {
            supplierId: supplier.id,
            supplierName: supplier.name,
            metrics: supplierMetrics,
            riskLevel,
            recommendations,
          };

          supplierReports.push(supplierReport);

          // Aggregate overall metrics
          overallMetrics.totalPayments += supplierMetrics.totalPayments;
          overallMetrics.onTimePayments += supplierMetrics.onTimePayments;
          overallMetrics.latePayments += supplierMetrics.latePayments;
          overallMetrics.overduePayments += supplierMetrics.overduePayments;
          overallMetrics.totalOutstandingAmount += supplierMetrics.totalOutstandingAmount;
          overallMetrics.criticalOverdueCount += supplierMetrics.criticalOverdueCount;
          overallMetrics.moderateOverdueCount += supplierMetrics.moderateOverdueCount;
          overallMetrics.recentOverdueCount += supplierMetrics.recentOverdueCount;

          this.logger.debug({
            supplierId: supplier.id,
            supplierName: supplier.name,
            metrics: supplierMetrics,
            riskLevel
          }, 'Supplier compliance metrics calculated');

        } catch (error) {
          this.logger.error({
            err: error,
            supplierId: supplier.id,
            supplierName: supplier.name
          }, 'Failed to calculate supplier metrics');
        }
      }

      // Calculate overall compliance rate and average payment days
      if (overallMetrics.totalPayments > 0) {
        overallMetrics.complianceRate = Math.round(
          (overallMetrics.onTimePayments / overallMetrics.totalPayments) * 100
        );
      }

      // Calculate weighted average payment days across all suppliers
      const totalPaymentDays = supplierReports.reduce(
        (sum, report) => sum + (report.metrics.averagePaymentDays * report.metrics.totalPayments),
        0
      );
      overallMetrics.averagePaymentDays = overallMetrics.totalPayments > 0
        ? Math.round(totalPaymentDays / overallMetrics.totalPayments)
        : 0;

      const report = {
        overallMetrics,
        supplierReports: supplierReports.sort((a, b) => {
          // Sort by risk level (CRITICAL first) then by outstanding amount
          const riskOrder = { CRITICAL: 4, HIGH: 3, MEDIUM: 2, LOW: 1 };
          if (riskOrder[a.riskLevel] !== riskOrder[b.riskLevel]) {
            return riskOrder[b.riskLevel] - riskOrder[a.riskLevel];
          }
          return b.metrics.totalOutstandingAmount - a.metrics.totalOutstandingAmount;
        }),
        generatedAt: new Date(),
      };

      this.logger.info({
        overallMetrics,
        supplierCount: supplierReports.length,
        highRiskSuppliers: supplierReports.filter(s => s.riskLevel === 'HIGH' || s.riskLevel === 'CRITICAL').length
      }, 'Payment compliance report generated successfully');

      return report;

    } catch (error) {
      this.logger.error({
        err: error
      }, 'Failed to generate payment compliance report');
      throw error;
    }
  }

  /**
   * Calculate payment compliance metrics for a specific supplier
   */
  private async calculateSupplierMetrics(supplierId: string): Promise<PaymentComplianceMetrics> {
    // Get all payments for this supplier
    const payments = await this.prisma.supplierPayment.findMany({
      where: {
        supplierId,
        status: PaymentStatus.PAID,
        purchaseOrderId: { not: null },
        effectivePaymentTerms: { not: null },
      },
      include: {
        purchaseOrder: {
          select: { orderDate: true }
        }
      }
    });

    // Get outstanding amounts
    const outstandingOrders = await this.prisma.purchaseOrder.findMany({
      where: {
        supplierId,
        paymentStatus: {
          in: [PaymentStatus.PENDING, PaymentStatus.PARTIAL, PaymentStatus.OVERDUE]
        }
      },
      include: {
        payments: {
          where: {
            status: {
              in: [PaymentStatus.PAID, PaymentStatus.PARTIAL]
            }
          },
          select: { amount: true }
        }
      }
    });

    let onTimePayments = 0;
    let latePayments = 0;
    let totalPaymentDays = 0;

    // Analyze payment timing
    for (const payment of payments) {
      if (payment.purchaseOrder && payment.effectivePaymentTerms !== null) {
        const orderDate = payment.purchaseOrder.orderDate;
        const paymentDate = payment.paymentDate;
        const actualDays = Math.floor(
          (paymentDate.getTime() - orderDate.getTime()) / (1000 * 60 * 60 * 24)
        );
        
        totalPaymentDays += actualDays;
        
        if (actualDays <= payment.effectivePaymentTerms) {
          onTimePayments++;
        } else {
          latePayments++;
        }
      }
    }

    // Calculate outstanding amounts and overdue counts
    let totalOutstandingAmount = 0;
    let criticalOverdueCount = 0;
    let moderateOverdueCount = 0;
    let recentOverdueCount = 0;

    for (const order of outstandingOrders) {
      const totalPaid = order.payments.reduce(
        (sum, payment) => sum + Number(payment.amount),
        0
      );
      const remainingAmount = Number(order.totalAmount) - totalPaid;
      totalOutstandingAmount += remainingAmount;

      // Check if overdue
      if (order.paymentStatus === PaymentStatus.OVERDUE) {
        const schedule = await this.paymentTermsService.getPaymentSchedule(order.id);
        if (schedule.daysOverdue > 60) {
          criticalOverdueCount++;
        } else if (schedule.daysOverdue > 30) {
          moderateOverdueCount++;
        } else {
          recentOverdueCount++;
        }
      }
    }

    const totalPayments = payments.length;
    const complianceRate = totalPayments > 0 
      ? Math.round((onTimePayments / totalPayments) * 100)
      : 100;
    const averagePaymentDays = totalPayments > 0 
      ? Math.round(totalPaymentDays / totalPayments)
      : 0;

    return {
      totalPayments,
      onTimePayments,
      latePayments,
      overduePayments: criticalOverdueCount + moderateOverdueCount + recentOverdueCount,
      complianceRate,
      averagePaymentDays,
      totalOutstandingAmount,
      criticalOverdueCount,
      moderateOverdueCount,
      recentOverdueCount,
    };
  }

  /**
   * Assess risk level based on metrics
   */
  private assessRiskLevel(metrics: PaymentComplianceMetrics): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
    // Critical: High outstanding amount with poor compliance or critical overdue
    if (metrics.criticalOverdueCount > 0 || 
        (metrics.totalOutstandingAmount > 100000000 && metrics.complianceRate < 50)) { // 100M IDR
      return 'CRITICAL';
    }

    // High: Moderate outstanding with poor compliance or moderate overdue
    if (metrics.moderateOverdueCount > 2 || 
        (metrics.totalOutstandingAmount > 50000000 && metrics.complianceRate < 70)) { // 50M IDR
      return 'HIGH';
    }

    // Medium: Some issues but manageable
    if (metrics.recentOverdueCount > 1 || 
        metrics.complianceRate < 85 || 
        metrics.totalOutstandingAmount > 25000000) { // 25M IDR
      return 'MEDIUM';
    }

    // Low: Good compliance and minimal outstanding
    return 'LOW';
  }

  /**
   * Generate recommendations based on metrics and risk level
   */
  private generateRecommendations(
    metrics: PaymentComplianceMetrics, 
    riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  ): string[] {
    const recommendations: string[] = [];

    if (riskLevel === 'CRITICAL') {
      recommendations.push('URGENT: Segera hubungi supplier untuk penyelesaian pembayaran yang sangat terlambat');
      recommendations.push('Pertimbangkan untuk menghentikan sementara pemesanan baru');
      recommendations.push('Evaluasi ulang limit kredit dan termin pembayaran');
    }

    if (riskLevel === 'HIGH') {
      recommendations.push('Hubungi supplier untuk membahas jadwal pembayaran');
      recommendations.push('Monitor ketat semua transaksi yang akan datang');
      recommendations.push('Pertimbangkan pengurangan termin pembayaran');
    }

    if (metrics.complianceRate < 80) {
      recommendations.push('Tingkatkan monitoring pembayaran untuk meningkatkan compliance rate');
      recommendations.push('Review proses internal pembayaran untuk efisiensi');
    }

    if (metrics.averagePaymentDays > 45) {
      recommendations.push('Rata-rata hari pembayaran terlalu tinggi, optimalkan cash flow management');
    }

    if (metrics.totalOutstandingAmount > 50000000) { // 50M IDR
      recommendations.push('Outstanding amount tinggi, prioritaskan pembayaran supplier ini');
    }

    if (recommendations.length === 0) {
      recommendations.push('Supplier dalam kondisi baik, pertahankan hubungan yang positif');
    }

    return recommendations;
  }

  /**
   * Log payment terms compliance event
   */
  async logComplianceEvent(
    eventType: 'PAYMENT_CREATED' | 'PAYMENT_OVERDUE' | 'TERMS_VIOLATED' | 'COMPLIANCE_IMPROVED',
    supplierId: string,
    details: Record<string, any>
  ) {
    const logData = {
      eventType,
      supplierId,
      timestamp: new Date(),
      ...details
    };

    switch (eventType) {
      case 'PAYMENT_CREATED':
        this.logger.info(logData, 'Payment created with terms compliance tracking');
        break;
      case 'PAYMENT_OVERDUE':
        this.logger.warn(logData, 'Payment overdue detected - compliance violation');
        break;
      case 'TERMS_VIOLATED':
        this.logger.error(logData, 'Payment terms violation detected');
        break;
      case 'COMPLIANCE_IMPROVED':
        this.logger.info(logData, 'Payment compliance improved for supplier');
        break;
    }
  }
}
