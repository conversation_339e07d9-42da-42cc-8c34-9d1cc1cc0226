import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
  Res,
  StreamableFile,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { Response } from 'express';
import { SuppliersService } from './suppliers.service';
import { PaymentTermsSchedulerService } from './services/payment-terms-scheduler.service';
import { CreateSupplierDto } from './dto/create-supplier.dto';
import { UpdateSupplierDto } from './dto/update-supplier.dto';
import { SupplierQueryDto } from './dto/supplier-query.dto';
import { CreateSupplierDocumentDto } from './dto/create-supplier-document.dto';
import { CreateSupplierPaymentDto } from './dto/create-supplier-payment.dto';
import { CreatePaymentProofDto } from './dto/create-payment-proof.dto';
import { ExportQueryDto } from './dto/import-supplier.dto';
import { ImportExportService } from './services/import-export.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AdminGuard } from '../settings/guards/admin.guard';
import { ManagerGuard } from './guards/manager.guard';
import { FileUploadService } from '../common/services/file-upload.service';
import { SupplierCodeGeneratorService } from './supplier-code-generator.service';

@Controller('suppliers')
@UseGuards(JwtAuthGuard)
export class SuppliersController {
  constructor(
    private readonly suppliersService: SuppliersService,
    private readonly paymentTermsSchedulerService: PaymentTermsSchedulerService,
    private readonly fileUploadService: FileUploadService,
    private readonly importExportService: ImportExportService,
    private readonly supplierCodeGeneratorService: SupplierCodeGeneratorService,
  ) {}

  @Post()
  @UseGuards(ManagerGuard) // Admin or Pharmacist can create
  async create(@Body() createSupplierDto: CreateSupplierDto, @Request() req) {
    return this.suppliersService.create(createSupplierDto, req.user.id);
  }

  @Get()
  async findAll(@Query() query: SupplierQueryDto) {
    return this.suppliersService.findAll(query);
  }

  @Get('stats')
  async getStats() {
    return this.suppliersService.getStats();
  }

  // Supplier code generation endpoints (must come before parameterized routes)
  @Get('generate-code/:type')
  async generateSupplierCode(@Param('type') type: string) {
    // Validate supplier type
    const validTypes = ['PBF', 'DISTRIBUTOR', 'MANUFACTURER', 'LOCAL'];
    if (!validTypes.includes(type)) {
      throw new BadRequestException('Jenis supplier tidak valid');
    }

    return {
      code: await this.supplierCodeGeneratorService.generateSupplierCode(type as any),
    };
  }

  @Get('validate-code/:code')
  async validateSupplierCode(@Param('code') code: string) {
    return {
      isUnique: await this.supplierCodeGeneratorService.validateCodeUniqueness(code),
    };
  }

  // Import/Export endpoints (must come before parameterized routes)
  @Get('template')
  @UseGuards(ManagerGuard)
  async downloadTemplate(@Res() res: Response) {
    try {
      const templatePath = await this.importExportService.generateTemplate();
      const fs = require('fs');

      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', 'attachment; filename="template-supplier.csv"');

      const fileStream = fs.createReadStream(templatePath);
      fileStream.pipe(res);
    } catch (error) {
      throw new BadRequestException(`Gagal mengunduh template: ${error.message}`);
    }
  }

  @Post('import')
  @UseGuards(ManagerGuard)
  @UseInterceptors(FileInterceptor('file', {
    storage: require('multer').diskStorage({
      destination: './uploads/imports',
      filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, `supplier-import-${uniqueSuffix}.csv`);
      },
    }),
    fileFilter: (req, file, cb) => {
      if (file.mimetype === 'text/csv' || file.mimetype === 'application/vnd.ms-excel') {
        cb(null, true);
      } else {
        cb(new BadRequestException('Hanya file CSV yang diizinkan'), false);
      }
    },
    limits: { fileSize: 5 * 1024 * 1024 }, // 5MB
  }))
  async importSuppliers(
    @UploadedFile() file: Express.Multer.File,
    @Request() req,
  ) {
    if (!file) {
      throw new BadRequestException('File CSV wajib diunggah');
    }

    try {
      const result = await this.importExportService.processImport(file.path, req.user.id);

      // Clean up uploaded file
      const fs = require('fs');
      if (fs.existsSync(file.path)) {
        fs.unlinkSync(file.path);
      }

      return result;
    } catch (error) {
      // Clean up uploaded file on error
      const fs = require('fs');
      if (file && fs.existsSync(file.path)) {
        fs.unlinkSync(file.path);
      }
      throw new BadRequestException(`Gagal memproses import: ${error.message}`);
    }
  }

  @Get('export')
  @UseGuards(ManagerGuard)
  async exportSuppliers(
    @Query() query: ExportQueryDto,
    @Res() res: Response,
  ) {
    try {
      const filePath = await this.importExportService.exportSuppliers(query);
      const fs = require('fs');

      const format = query.format || 'csv';
      const mimeType = format === 'xlsx'
        ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        : 'text/csv';

      const timestamp = new Date().toISOString().split('T')[0];
      const filename = `suppliers-export-${timestamp}.${format}`;

      res.setHeader('Content-Type', mimeType);
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

      const fileStream = fs.createReadStream(filePath);
      fileStream.pipe(res);

      // Clean up file after sending
      fileStream.on('end', () => {
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
        }
      });
    } catch (error) {
      throw new BadRequestException(`Gagal mengekspor data: ${error.message}`);
    }
  }

  @Patch(':id/deactivate')
  @UseGuards(ManagerGuard) // Admin or Pharmacist can deactivate
  async deactivate(@Param('id') id: string, @Request() req) {
    return this.suppliersService.deactivate(id, req.user.id);
  }

  @Patch(':id/activate')
  @UseGuards(ManagerGuard) // Admin or Pharmacist can activate
  async activate(@Param('id') id: string, @Request() req) {
    return this.suppliersService.activate(id, req.user.id);
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    return this.suppliersService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(ManagerGuard) // Admin or Pharmacist can update
  async update(
    @Param('id') id: string,
    @Body() updateSupplierDto: UpdateSupplierDto,
    @Request() req,
  ) {
    return this.suppliersService.update(id, updateSupplierDto, req.user.id);
  }

  @Delete(':id')
  @UseGuards(AdminGuard) // Only Admin can hard delete
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id') id: string) {
    await this.suppliersService.hardDelete(id);
  }

  // Document management endpoints
  @Get(':id/documents')
  async getDocuments(
    @Param('id') id: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('type') type?: string,
    @Query('search') search?: string,
  ) {
    return this.suppliersService.getDocuments(id, { page, limit, type, search });
  }

  @Get(':id/documents/:documentId')
  async getDocument(
    @Param('id') id: string,
    @Param('documentId') documentId: string,
  ) {
    return this.suppliersService.getDocument(id, documentId);
  }

  @Get(':id/documents/:documentId/download')
  async downloadDocument(
    @Param('id') id: string,
    @Param('documentId') documentId: string,
  ) {
    return this.suppliersService.downloadDocument(id, documentId);
  }

  @Post(':id/documents')
  @UseGuards(ManagerGuard)
  async createDocument(
    @Param('id') id: string,
    @Body() createDocumentDto: CreateSupplierDocumentDto,
    @Request() req,
  ) {
    return this.suppliersService.createDocument(id, createDocumentDto, req.user.id);
  }

  @Patch(':id/documents/:documentId')
  @UseGuards(ManagerGuard)
  async updateDocument(
    @Param('id') id: string,
    @Param('documentId') documentId: string,
    @Body() updateDocumentDto: Partial<CreateSupplierDocumentDto>,
    @Request() req,
  ) {
    return this.suppliersService.updateDocument(id, documentId, updateDocumentDto, req.user.id);
  }

  @Delete(':id/documents/:documentId')
  @UseGuards(ManagerGuard)
  @HttpCode(HttpStatus.NO_CONTENT)
  async deleteDocument(
    @Param('id') id: string,
    @Param('documentId') documentId: string,
    @Request() req,
  ) {
    await this.suppliersService.deleteDocument(id, documentId, req.user.id);
  }

  @Delete(':id/documents')
  @UseGuards(ManagerGuard)
  @HttpCode(HttpStatus.NO_CONTENT)
  async deleteMultipleDocuments(
    @Param('id') id: string,
    @Body() body: { documentIds: string[] },
    @Request() req,
  ) {
    await this.suppliersService.deleteMultipleDocuments(id, body.documentIds, req.user.id);
  }

  @Post(':id/documents/upload')
  @UseGuards(ManagerGuard)
  @UseInterceptors(FileInterceptor('file', {
    storage: require('multer').diskStorage({
      destination: (req, file, cb) => {
        const uploadPath = './uploads/supplier-documents';
        if (!require('fs').existsSync(uploadPath)) {
          require('fs').mkdirSync(uploadPath, { recursive: true });
        }
        cb(null, uploadPath);
      },
      filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const ext = require('path').extname(file.originalname);
        cb(null, `${file.fieldname}-${uniqueSuffix}${ext}`);
      },
    }),
    fileFilter: (req, file, cb) => {
      // Use the same file filter logic as FileUploadService
      const allowedMimes = [
        'application/pdf',
        'image/jpeg',
        'image/png',
        'image/jpg',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      ];

      if (allowedMimes.includes(file.mimetype)) {
        cb(null, true);
      } else {
        cb(new BadRequestException('Tipe file tidak diizinkan. Hanya file PDF, JPG, PNG, DOC, DOCX, XLS, dan XLSX yang didukung.'), false);
      }
    },
    limits: {
      fileSize: 10 * 1024 * 1024, // 10MB limit
    },
  }))
  async uploadDocument(
    @Param('id') id: string,
    @UploadedFile() file: Express.Multer.File,
    @Body() body: { type: string; name: string; description?: string },
    @Request() req,
  ) {
    if (!file) {
      throw new BadRequestException('File wajib diunggah');
    }

    const createDocumentDto: CreateSupplierDocumentDto = {
      type: body.type as any,
      name: body.name,
      description: body.description,
      fileName: file.originalname,
      filePath: this.fileUploadService.getSupplierDocumentFileUrl(file.filename),
      fileSize: file.size,
      mimeType: file.mimetype,
    };

    return this.suppliersService.createDocument(id, createDocumentDto, req.user.id);
  }

  // Payment reference number generation endpoints (must come before parameterized routes)
  @Get('payments/generate-reference')
  async generatePaymentReference() {
    return {
      reference: await this.suppliersService.generatePaymentReference(),
    };
  }

  @Get('payments/validate-reference/:reference')
  async validatePaymentReference(@Param('reference') reference: string) {
    return {
      isUnique: await this.suppliersService.validatePaymentReference(reference),
    };
  }

  // Payment management endpoints
  @Get(':id/payments')
  async getPayments(
    @Param('id') id: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('status') status?: string,
    @Query('paymentMethod') paymentMethod?: string,
    @Query('search') search?: string,
    @Query('dateFrom') dateFrom?: string,
    @Query('dateTo') dateTo?: string,
  ) {
    return this.suppliersService.getPayments(id, {
      page,
      limit,
      status,
      paymentMethod,
      search,
      dateFrom,
      dateTo,
    });
  }

  @Get(':id/payments/summary')
  async getPaymentSummary(@Param('id') id: string) {
    return this.suppliersService.getPaymentSummary(id);
  }

  @Get(':id/payments/:paymentId')
  async getPayment(
    @Param('id') id: string,
    @Param('paymentId') paymentId: string,
  ) {
    return this.suppliersService.getPayment(id, paymentId);
  }

  @Post(':id/payments')
  @UseGuards(ManagerGuard)
  async createPayment(
    @Param('id') id: string,
    @Body() createPaymentDto: CreateSupplierPaymentDto,
    @Request() req,
  ) {
    return this.suppliersService.createPayment(id, createPaymentDto, req.user.id);
  }

  @Patch(':id/payments/:paymentId')
  @UseGuards(ManagerGuard)
  async updatePayment(
    @Param('id') id: string,
    @Param('paymentId') paymentId: string,
    @Body() updatePaymentDto: Partial<CreateSupplierPaymentDto>,
    @Request() req,
  ) {
    return this.suppliersService.updatePayment(id, paymentId, updatePaymentDto, req.user.id);
  }

  @Delete(':id/payments/:paymentId')
  @UseGuards(ManagerGuard)
  @HttpCode(HttpStatus.NO_CONTENT)
  async deletePayment(
    @Param('id') id: string,
    @Param('paymentId') paymentId: string,
    @Request() req,
  ) {
    await this.suppliersService.deletePayment(id, paymentId, req.user.id);
  }

  // Payment Proof management endpoints
  @Get(':id/payments/:paymentId/proof')
  async getPaymentProof(
    @Param('id') id: string,
    @Param('paymentId') paymentId: string,
  ) {
    const result = await this.suppliersService.getPaymentProof(id, paymentId);
    return result || null;
  }

  @Post(':id/payments/:paymentId/proof')
  @UseGuards(ManagerGuard)
  @UseInterceptors(FileInterceptor('file', {
    storage: require('multer').diskStorage({
      destination: './uploads/payment-proofs',
      filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const ext = require('path').extname(file.originalname);
        cb(null, `payment-proof-${uniqueSuffix}${ext}`);
      },
    }),
    fileFilter: (req, file, cb) => {
      const allowedMimes = [
        'application/pdf',
        'image/jpeg',
        'image/png',
        'image/jpg',
      ];
      if (allowedMimes.includes(file.mimetype)) {
        cb(null, true);
      } else {
        cb(new BadRequestException('Tipe file tidak diizinkan. Hanya file PDF, JPG, dan PNG yang didukung.'), false);
      }
    },
    limits: { fileSize: 10 * 1024 * 1024 }, // 10MB
  }))
  async uploadPaymentProof(
    @Param('id') id: string,
    @Param('paymentId') paymentId: string,
    @UploadedFile() file: Express.Multer.File,
    @Body() body: { name: string; description?: string },
    @Request() req,
  ) {
    if (!file) {
      throw new BadRequestException('File wajib diunggah');
    }

    const createPaymentProofDto: CreatePaymentProofDto = {
      name: body.name,
      description: body.description,
      fileName: file.originalname,
      filePath: this.fileUploadService.getPaymentProofFileUrl(file.filename),
      fileSize: file.size,
      mimeType: file.mimetype,
    };

    return this.suppliersService.createPaymentProof(id, paymentId, createPaymentProofDto, req.user.id);
  }

  @Delete(':id/payments/:paymentId/proof')
  @UseGuards(ManagerGuard)
  @HttpCode(HttpStatus.NO_CONTENT)
  async deletePaymentProof(
    @Param('id') id: string,
    @Param('paymentId') paymentId: string,
    @Request() req,
  ) {
    await this.suppliersService.deletePaymentProof(id, paymentId, req.user.id);
  }

  // Payment Terms management endpoints
  @Get(':id/payment-terms-summary')
  @UseGuards(ManagerGuard) // Admin or Pharmacist can view payment terms summary
  async getPaymentTermsSummary(@Param('id') id: string) {
    return this.suppliersService.getPaymentTermsSummary(id);
  }

  @Post(':id/payments/validate-terms')
  @UseGuards(ManagerGuard) // Admin or Pharmacist can validate payment terms
  async validatePaymentTerms(
    @Param('id') id: string,
    @Body() createPaymentDto: CreateSupplierPaymentDto,
    @Request() req,
  ) {
    return this.suppliersService.validatePaymentTerms(id, createPaymentDto, req.user.id);
  }

  // Overdue Payment Management endpoints
  @Get('overdue-payments')
  @UseGuards(ManagerGuard) // Admin or Pharmacist can view overdue payments
  async getOverduePayments() {
    return this.paymentTermsSchedulerService.getOverdueStatistics();
  }

  @Post('overdue-payments/update-status')
  @UseGuards(AdminGuard) // Only Admin can trigger status updates
  async updateOverduePaymentStatuses() {
    return this.paymentTermsSchedulerService.triggerOverdueUpdate();
  }

  @Post('overdue-payments/generate-report')
  @UseGuards(ManagerGuard) // Admin or Pharmacist can generate reports
  async generateOverdueReport() {
    return this.paymentTermsSchedulerService.triggerOverdueReport();
  }

}
