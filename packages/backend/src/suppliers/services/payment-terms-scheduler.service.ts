import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { PinoLogger, InjectPinoLogger } from 'nestjs-pino';
import { PaymentTermsService } from './payment-terms.service';

@Injectable()
export class PaymentTermsSchedulerService {
  constructor(
    private paymentTermsService: PaymentTermsService,
    @InjectPinoLogger(PaymentTermsSchedulerService.name)
    private readonly logger: PinoLogger,
  ) {}

  /**
   * Scheduled task to update overdue payment statuses
   * Runs daily at 6:00 AM Indonesian time (WIB)
   */
  @Cron('0 6 * * *', {
    name: 'updateOverduePayments',
    timeZone: 'Asia/Jakarta',
  })
  async handleUpdateOverduePayments() {
    this.logger.info({}, 'Starting scheduled overdue payment status update');

    try {
      const result = await this.paymentTermsService.updateOverduePaymentStatuses();

      this.logger.info({
        updated: result.updated,
        errors: result.errors,
        message: 'Scheduled overdue payment status update completed'
      }, 'Overdue payment status update completed successfully');

      return result;
    } catch (error) {
      this.logger.error({
        err: error,
        message: 'Scheduled overdue payment status update failed'
      }, 'Failed to update overdue payment statuses');
      
      // Don't throw error to prevent scheduler from stopping
      return { updated: 0, errors: 1 };
    }
  }

  /**
   * Scheduled task to generate overdue payment reports
   * Runs every Monday at 8:00 AM Indonesian time (WIB)
   */
  @Cron('0 8 * * 1', {
    name: 'generateOverdueReport',
    timeZone: 'Asia/Jakarta',
  })
  async handleGenerateOverdueReport() {
    this.logger.info({}, 'Starting scheduled overdue payment report generation');

    try {
      const overduePayments = await this.paymentTermsService.getOverduePayments();

      if (overduePayments.length > 0) {
        const totalOverdueAmount = overduePayments.reduce(
          (sum, payment) => sum + payment.remainingAmount,
          0
        );

        const reportSummary = {
          totalOverdueOrders: overduePayments.length,
          totalOverdueAmount,
          averageDaysOverdue: Math.round(
            overduePayments.reduce((sum, payment) => sum + payment.daysOverdue, 0) / 
            overduePayments.length
          ),
          criticalOverdue: overduePayments.filter(p => p.daysOverdue > 60).length,
          moderateOverdue: overduePayments.filter(p => p.daysOverdue > 30 && p.daysOverdue <= 60).length,
          recentOverdue: overduePayments.filter(p => p.daysOverdue <= 30).length,
        };

        this.logger.warn({
          reportSummary,
          message: 'Weekly overdue payment report generated'
        }, 'Overdue payments detected - action required');

        // Here you could integrate with email service, notification service, etc.
        // For now, we just log the report
      } else {
        this.logger.info({
          message: 'No overdue payments found'
        }, 'Weekly overdue payment report - all payments current');
      }

      return {
        success: true,
        overdueCount: overduePayments.length,
        reportGenerated: true,
      };
    } catch (error) {
      this.logger.error({
        err: error,
        message: 'Scheduled overdue payment report generation failed'
      }, 'Failed to generate overdue payment report');
      
      return {
        success: false,
        overdueCount: 0,
        reportGenerated: false,
      };
    }
  }

  /**
   * Manual trigger for overdue payment status update
   * Can be called via API endpoint for immediate processing
   */
  async triggerOverdueUpdate() {
    this.logger.info({}, 'Manual trigger for overdue payment status update');
    return this.handleUpdateOverduePayments();
  }

  /**
   * Manual trigger for overdue payment report generation
   * Can be called via API endpoint for immediate report generation
   */
  async triggerOverdueReport() {
    this.logger.info({}, 'Manual trigger for overdue payment report generation');
    return this.handleGenerateOverdueReport();
  }

  /**
   * Get current overdue payment statistics
   * Useful for dashboard and monitoring
   */
  async getOverdueStatistics() {
    this.logger.trace({}, 'Getting overdue payment statistics');

    try {
      const overduePayments = await this.paymentTermsService.getOverduePayments();

      const statistics = {
        totalOverdueOrders: overduePayments.length,
        totalOverdueAmount: overduePayments.reduce(
          (sum, payment) => sum + payment.remainingAmount,
          0
        ),
        averageDaysOverdue: overduePayments.length > 0 
          ? Math.round(
              overduePayments.reduce((sum, payment) => sum + payment.daysOverdue, 0) / 
              overduePayments.length
            )
          : 0,
        overdueByCategory: {
          critical: overduePayments.filter(p => p.daysOverdue > 60).length, // > 60 days
          moderate: overduePayments.filter(p => p.daysOverdue > 30 && p.daysOverdue <= 60).length, // 31-60 days
          recent: overduePayments.filter(p => p.daysOverdue <= 30).length, // <= 30 days
        },
        overdueBySupplier: this.groupOverdueBySupplier(overduePayments),
        lastUpdated: new Date(),
      };

      this.logger.debug({
        statistics,
        message: 'Overdue payment statistics calculated'
      }, 'Overdue statistics retrieved');

      return statistics;
    } catch (error) {
      this.logger.error({
        err: error,
        message: 'Failed to get overdue payment statistics'
      }, 'Error calculating overdue statistics');
      throw error;
    }
  }

  /**
   * Helper method to group overdue payments by supplier
   */
  private groupOverdueBySupplier(overduePayments: any[]) {
    const supplierGroups = overduePayments.reduce((groups, payment) => {
      const key = `${payment.purchaseOrderId}`; // We'll need to get supplier info from purchase order
      if (!groups[key]) {
        groups[key] = {
          count: 0,
          totalAmount: 0,
          maxDaysOverdue: 0,
        };
      }
      
      groups[key].count++;
      groups[key].totalAmount += payment.remainingAmount;
      groups[key].maxDaysOverdue = Math.max(groups[key].maxDaysOverdue, payment.daysOverdue);
      
      return groups;
    }, {});

    // Convert to array and sort by total amount descending
    return Object.entries(supplierGroups)
      .map(([key, data]: [string, any]) => ({
        purchaseOrderId: key,
        ...data,
      }))
      .sort((a, b) => b.totalAmount - a.totalAmount)
      .slice(0, 10); // Top 10 suppliers with overdue payments
  }
}
